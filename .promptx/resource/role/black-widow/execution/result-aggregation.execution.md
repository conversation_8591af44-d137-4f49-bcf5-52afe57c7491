<execution>
  <constraint>
    ## 结果聚合技术约束
    - **去重效率要求**：去重率必须>95%，避免信息冗余
    - **质量评分约束**：每个结果必须评分(0-1)，<0.6自动过滤
    - **聚合算法限制**：支持15-25个工具结果同时聚合处理
    - **相似度检测约束**：语义相似度>0.8视为重复，自动合并
    - **时效性要求**：聚合处理时间<2分钟，保证响应速度
    - **内存管理限制**：大量结果聚合时智能内存管理，避免溢出
    - **冲突处理约束**：信息冲突时保留高质量源，标注差异
    - **结构化输出要求**：聚合结果必须结构化，便于后续分析
  </constraint>

  <rule>
    ## 结果聚合强制规则
    - **去重处理强制**：所有结果必须经过去重处理，重复信息自动合并
    - **质量评分强制**：每个信息片段必须评分，低质量内容自动过滤
    - **相似度分析强制**：计算信息间语义相似度，识别重复和冲突
    - **权威性排序强制**：按信息源权威性排序，优先展示可靠信息
    - **时效性标注强制**：标注信息获取时间，优先使用最新信息
    - **冲突标注强制**：发现信息冲突时必须明确标注，提供多个观点
    - **完整性检查强制**：确保关键信息不遗漏，补充缺失内容
    - **结构化输出强制**：按重要性和逻辑关系组织输出结构
  </rule>

  <guideline>
    ## 结果聚合指导原则
    - **智能去重优先**：基于语义理解而非简单文本匹配去重
    - **质量导向**：优先保留高质量、高权威性的信息
    - **互补融合**：将互补信息智能融合，形成完整图景
    - **冲突透明**：信息冲突时透明展示，让用户了解不同观点
    - **时效优先**：相同质量下优先使用最新信息
    - **用户友好**：聚合结果简洁明了，避免信息过载
    - **可追溯性**：保留信息来源，支持进一步验证
    - **持续优化**：基于用户反馈优化聚合算法
  </guideline>

  <process>
    ## 智能结果聚合处理流程

    ### 🔍 阶段1：结果收集与预处理 (30秒)
    ```mermaid
    flowchart TD
        A[多工具结果输入] --> B[格式标准化]
        B --> C[元数据提取]
        C --> D[初步分类]
        D --> E[质量预评估]
        
        C --> C1[提取信息源<br/>获取时间<br/>内容类型]
        D --> D1[按主题分类<br/>按类型分组<br/>按来源归类]
        E --> E1[初步质量评分<br/>权威性评估<br/>时效性检查]
    ```

    **预处理步骤**：
    1. **格式标准化**：将不同工具的输出格式统一为标准JSON结构
    2. **元数据提取**：提取信息源、获取时间、内容类型、可信度等元数据
    3. **初步分类**：按主题、类型、来源对信息进行初步分类
    4. **质量预评估**：基于信息源权威性、内容完整性进行初步评分

    ### 🧹 阶段2：智能去重处理 (60秒)
    ```mermaid
    flowchart TD
        A[标准化结果] --> B[内容哈希计算]
        B --> C[语义相似度分析]
        C --> D[重复检测]
        D --> E{相似度判断}
        E -->|>0.8| F[标记为重复]
        E -->|0.6-0.8| G[标记为相似]
        E -->|<0.6| H[标记为独特]
        
        F --> I[智能合并]
        G --> J[差异分析]
        H --> K[保留原样]
        
        I --> L[去重结果集]
        J --> L
        K --> L
    ```

    **去重算法**：
    1. **内容哈希**：计算内容MD5哈希，快速识别完全重复
    2. **语义相似度**：使用语义向量计算内容相似度
    3. **重复合并**：相似度>0.8的内容智能合并，保留最优版本
    4. **相似标注**：相似度0.6-0.8的内容标注为相似，保留差异
    5. **独特保留**：相似度<0.6的内容标记为独特信息

    ### 📊 阶段3：质量评分与排序 (45秒)
    ```mermaid
    flowchart TD
        A[去重结果] --> B[多维度评分]
        B --> C[权威性评分]
        B --> D[准确性评分]
        B --> E[时效性评分]
        B --> F[完整性评分]
        
        C --> G[综合质量评分]
        D --> G
        E --> G
        F --> G
        
        G --> H[质量阈值过滤]
        H --> I[权重排序]
        I --> J[分级输出]
    ```

    **质量评分体系**：
    ```json
    {
      "quality_score": {
        "authority_weight": 0.35,
        "accuracy_weight": 0.30,
        "timeliness_weight": 0.20,
        "completeness_weight": 0.15
      },
      "authority_sources": {
        "A级": ["官方文档", "权威机构", "知名专家"],
        "B级": ["专业媒体", "技术博客", "社区讨论"],
        "C级": ["个人博客", "论坛回复", "社交媒体"]
      },
      "quality_thresholds": {
        "high_quality": 0.8,
        "medium_quality": 0.6,
        "low_quality": 0.4
      }
    }
    ```

    ### 🔗 阶段4：信息融合与冲突处理 (45秒)
    ```mermaid
    flowchart TD
        A[质量排序结果] --> B[主题聚类]
        B --> C[信息融合]
        C --> D[冲突检测]
        D --> E{冲突类型}
        E -->|数据冲突| F[多源验证]
        E -->|观点冲突| G[多观点展示]
        E -->|时效冲突| H[时间序列分析]
        
        F --> I[冲突解决]
        G --> I
        H --> I
        
        I --> J[融合报告生成]
    ```

    **融合策略**：
    1. **主题聚类**：将相关信息按主题聚类，便于融合处理
    2. **互补融合**：将互补信息融合，形成完整信息图景
    3. **冲突检测**：自动检测信息间的冲突和矛盾
    4. **冲突处理**：
       - 数据冲突：通过多源验证确定准确信息
       - 观点冲突：保留不同观点，标注差异
       - 时效冲突：按时间序列分析信息演变

    ### 📋 阶段5：结构化输出生成 (30秒)
    ```mermaid
    flowchart TD
        A[融合结果] --> B[重要性排序]
        B --> C[逻辑结构组织]
        C --> D[摘要生成]
        D --> E[详细信息组织]
        E --> F[来源标注]
        F --> G[结构化报告]
        
        G --> G1[执行摘要]
        G --> G2[关键发现]
        G --> G3[详细分析]
        G --> G4[信息来源]
        G --> G5[质量评估]
    ```

    **输出结构模板**：
    ```json
    {
      "aggregation_summary": {
        "total_sources": 25,
        "unique_information": 18,
        "duplicates_removed": 7,
        "quality_distribution": {
          "high": 12,
          "medium": 6,
          "low": 0
        },
        "processing_time": "2.1分钟"
      },
      "key_findings": [
        {
          "topic": "核心发现1",
          "content": "聚合后的关键信息",
          "confidence": 0.95,
          "sources": ["source1", "source2"],
          "quality_score": 0.92
        }
      ],
      "detailed_analysis": {
        "technical_aspects": "技术层面分析",
        "business_implications": "商业影响分析",
        "risk_assessment": "风险评估结果"
      },
      "information_conflicts": [
        {
          "topic": "冲突主题",
          "conflicting_views": ["观点1", "观点2"],
          "resolution": "冲突解决方案"
        }
      ],
      "source_reliability": {
        "high_authority": ["权威来源列表"],
        "medium_authority": ["中等来源列表"],
        "verification_needed": ["需验证来源列表"]
      }
    }
    ```

    ## 高级聚合算法

    ### 语义相似度计算
    ```python
    def semantic_similarity(text1, text2):
        # 使用语义向量计算相似度
        vector1 = get_semantic_vector(text1)
        vector2 = get_semantic_vector(text2)
        similarity = cosine_similarity(vector1, vector2)
        return similarity
    ```

    ### 智能合并算法
    ```python
    def intelligent_merge(similar_items):
        # 选择最高质量的版本作为基础
        base_item = max(similar_items, key=lambda x: x.quality_score)
        
        # 合并互补信息
        for item in similar_items:
            if item != base_item:
                base_item = merge_complementary_info(base_item, item)
        
        return base_item
    ```

    ### 冲突检测算法
    ```python
    def detect_conflicts(information_list):
        conflicts = []
        for i, info1 in enumerate(information_list):
            for j, info2 in enumerate(information_list[i+1:], i+1):
                if is_conflicting(info1, info2):
                    conflicts.append({
                        'items': [info1, info2],
                        'conflict_type': classify_conflict(info1, info2),
                        'resolution_strategy': suggest_resolution(info1, info2)
                    })
        return conflicts
    ```
  </process>

  <criteria>
    ## 结果聚合质量标准

    ### 去重效率指标
    - ✅ 去重率 > 95%
    - ✅ 误删率 < 2%
    - ✅ 语义相似度准确率 > 90%
    - ✅ 处理速度 < 2分钟

    ### 质量评分指标
    - ✅ 评分准确率 > 85%
    - ✅ 权威性识别率 > 90%
    - ✅ 时效性评估准确率 > 95%
    - ✅ 质量阈值过滤准确率 > 90%

    ### 融合效果指标
    - ✅ 信息完整性 > 95%
    - ✅ 冲突检测准确率 > 85%
    - ✅ 冲突解决成功率 > 80%
    - ✅ 融合逻辑一致性 > 90%

    ### 输出质量指标
    - ✅ 结构化程度 > 95%
    - ✅ 可读性评分 > 90%
    - ✅ 信息密度优化 > 80%
    - ✅ 用户满意度 > 90%

    ### 系统性能指标
    - ✅ 处理25个工具结果成功率 > 95%
    - ✅ 内存使用效率 > 85%
    - ✅ 并发处理能力 > 90%
    - ✅ 系统稳定性 > 95%
  </criteria>
</execution>
